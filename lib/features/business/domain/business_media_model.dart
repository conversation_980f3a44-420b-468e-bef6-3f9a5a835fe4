class BusinessMediaModel {
  final int id;
  final String mediaUrl;
  final String mediaType; // 'image' or 'video'
  final String? caption;
  final DateTime? createdAt;

  BusinessMediaModel({
    required this.id,
    required this.mediaUrl,
    required this.mediaType,
    this.caption,
    this.createdAt,
  });

  factory BusinessMediaModel.fromJson(Map<String, dynamic> json) {
    return BusinessMediaModel(
      id: json['id'] as int? ?? 0,
      mediaUrl: json['media_url'] as String,
      mediaType: json['media_type'] as String,
      caption: json['caption'] as String?,
      createdAt: json['created_at'] != null
          ? DateTime.tryParse(json['created_at'] as String)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'media_url': mediaUrl,
      'media_type': mediaType,
      'caption': caption,
      'created_at': createdAt?.toIso8601String(),
    };
  }

  bool get isImage => mediaType == 'image';
  bool get isVideo => mediaType == 'video';

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is BusinessMediaModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
