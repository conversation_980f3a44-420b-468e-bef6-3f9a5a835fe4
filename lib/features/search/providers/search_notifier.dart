import 'dart:async';
import 'package:flutter/foundation.dart';
import '../../business/data/business_repository.dart';
import '../../business/domain/business_summary_model.dart';

class SearchFilters {
  final String? categorySlug;
  final double? latitude;
  final double? longitude;
  final int? rating;

  SearchFilters({
    this.categorySlug,
    this.latitude,
    this.longitude,
    this.rating,
  });
}

class SearchNotifier extends ChangeNotifier {
  final BusinessRepository _businessRepository;

  bool _isLoading = false;
  List<BusinessSummaryModel> _results = [];
  String? _errorMessage;
  bool _initialSearch = true;
  Timer? _debounceTimer;
  String _currentQuery = '';

  SearchNotifier(this._businessRepository);

  // Getters
  bool get isLoading => _isLoading;
  List<BusinessSummaryModel> get results => _results;
  String? get errorMessage => _errorMessage;
  bool get initialSearch => _initialSearch;
  String get currentQuery => _currentQuery;

  // Clear error message
  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  // Clear results
  void clearResults() {
    _results = [];
    _initialSearch = true;
    _currentQuery = '';
    _errorMessage = null;
    notifyListeners();
  }

  // Search with debounce
  Future<void> search(String query, {SearchFilters? filters}) async {
    _currentQuery = query;

    // Cancel previous timer
    _debounceTimer?.cancel();

    // If query is empty, clear results
    if (query.trim().isEmpty) {
      _results = [];
      _initialSearch = true;
      _errorMessage = null;
      notifyListeners();
      return;
    }

    // Set up debounce timer
    _debounceTimer = Timer(const Duration(milliseconds: 500), () {
      _performSearch(query, filters: filters);
    });
  }

  // Perform the actual search
  Future<void> _performSearch(String query, {SearchFilters? filters}) async {
    _isLoading = true;
    _errorMessage = null;
    _initialSearch = false;
    notifyListeners();

    try {
      final results = await _businessRepository.getBusinesses(
        query: query,
        category: filters?.categorySlug,
        latitude: filters?.latitude,
        longitude: filters?.longitude,
        rating: filters?.rating,
      );

      _results = results;
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _errorMessage = e.toString().replaceFirst('Exception: ', '');
      _isLoading = false;
      notifyListeners();
    }
  }

  // Search by category
  Future<void> searchByCategory(
    String categorySlug, {
    SearchFilters? filters,
  }) async {
    _isLoading = true;
    _errorMessage = null;
    _initialSearch = false;
    notifyListeners();

    try {
      final results = await _businessRepository.getBusinesses(
        category: categorySlug,
        latitude: filters?.latitude,
        longitude: filters?.longitude,
        rating: filters?.rating,
      );

      _results = results;
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _errorMessage = e.toString().replaceFirst('Exception: ', '');
      _isLoading = false;
      notifyListeners();
    }
  }

  @override
  void dispose() {
    _debounceTimer?.cancel();
    super.dispose();
  }
}
