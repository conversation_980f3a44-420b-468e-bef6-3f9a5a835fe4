import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:dio/dio.dart';

import 'core/theme/app_theme.dart';
import 'core/api/api_client.dart';
import 'core/router/app_router.dart';
import 'features/auth/providers/auth_notifier.dart';
import 'features/auth/data/auth_repository.dart';
import 'features/search/providers/search_notifier.dart';
import 'features/profile/providers/favorites_notifier.dart';
import 'features/business/data/business_repository.dart';
import 'features/business/data/review_repository.dart';
import 'features/business/data/media_repository.dart';
import 'features/business/providers/review_notifier.dart';
import 'features/business/providers/media_notifier.dart';
import 'features/profile/data/user_repository.dart';

void main() {
  runApp(const LocalFindApp());
}

class LocalFindApp extends StatelessWidget {
  const LocalFindApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        // Base Dio instance
        Provider<Dio>(create: (context) => ApiClient.createDio()),

        // Auth Notifier
        ChangeNotifierProvider<AuthNotifier>(
          create: (context) =>
              AuthNotifier(AuthRepository(context.read<Dio>())),
        ),

        // Authenticated Dio instance (depends on AuthNotifier)
        ProxyProvider<AuthNotifier, Dio>(
          update: (context, authNotifier, _) =>
              ApiClient.createAuthenticatedDio(authNotifier.token),
        ),

        // Repositories
        ProxyProvider<Dio, BusinessRepository>(
          update: (context, dio, _) => BusinessRepository(dio),
        ),

        ProxyProvider<Dio, ReviewRepository>(
          update: (context, dio, _) => ReviewRepository(dio),
        ),

        ProxyProvider<Dio, MediaRepository>(
          update: (context, dio, _) => MediaRepository(dio),
        ),

        ProxyProvider<Dio, UserRepository>(
          update: (context, dio, _) => UserRepository(dio),
        ),

        // Other Notifiers
        ChangeNotifierProvider<SearchNotifier>(
          create: (context) =>
              SearchNotifier(context.read<BusinessRepository>()),
        ),

        ChangeNotifierProvider<ReviewNotifier>(
          create: (context) => ReviewNotifier(context.read<ReviewRepository>()),
        ),

        ChangeNotifierProvider<MediaNotifier>(
          create: (context) => MediaNotifier(context.read<MediaRepository>()),
        ),

        ChangeNotifierProvider<FavoritesNotifier>(
          create: (context) =>
              FavoritesNotifier(context.read<UserRepository>()),
        ),
      ],
      child: Consumer<AuthNotifier>(
        builder: (context, authNotifier, child) {
          return MaterialApp.router(
            title: 'LocalFind',
            theme: AppTheme.lightTheme,
            routerConfig: AppRouter.createRouter(),
            debugShowCheckedModeBanner: false,
          );
        },
      ),
    );
  }
}
